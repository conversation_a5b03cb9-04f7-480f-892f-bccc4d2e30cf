import { useEffect, useState } from 'react';
import styles from '@/styles/SquarePaymentForm.module.css';

/**
 * SquarePaymentForm component for Square checkout
 * 
 * @param {Object} props - Component props
 * @param {number} props.amount - Amount to charge
 * @param {string} props.currency - Currency code (default: AUD)
 * @param {Function} props.onSuccess - Function to call on successful payment
 * @param {Function} props.onError - Function to call on payment error
 * @param {Object} props.orderDetails - Order details for Square
 * @returns {JSX.Element}
 */
const SquarePaymentForm = ({ 
  amount, 
  currency = 'AUD', 
  onSuccess, 
  onError,
  orderDetails = {}
}) => {
  const [paymentForm, setPaymentForm] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [errorMessage, setErrorMessage] = useState('');
  
  // Initialize Square payment form
  useEffect(() => {
    // Load Square Web Payments SDK with environment-aware URL
    if (!window.Square) {
      const script = document.createElement('script');

      // Determine environment and SDK URL (matching POS Terminal pattern)
      const isProduction = process.env.NODE_ENV === 'production' &&
                          process.env.NEXT_PUBLIC_SQUARE_ENVIRONMENT === 'production';
      const sdkUrl = isProduction
        ? 'https://web.squarecdn.com/v1/square.js'
        : 'https://sandbox.web.squarecdn.com/v1/square.js';

      console.log('Loading Square SDK for public checkout:', {
        environment: isProduction ? 'production' : 'sandbox',
        sdkUrl,
        nodeEnv: process.env.NODE_ENV,
        squareEnv: process.env.NEXT_PUBLIC_SQUARE_ENVIRONMENT,
        appId: process.env.NEXT_PUBLIC_SQUARE_APPLICATION_ID?.substring(0, 10) + '...'
      });

      script.src = sdkUrl;
      script.onload = initializePaymentForm;
      script.onerror = () => {
        setIsLoading(false);
        setErrorMessage('Failed to load Square payment form. Please try again later.');
      };
      document.body.appendChild(script);

      return () => {
        if (document.body.contains(script)) {
          document.body.removeChild(script);
        }
      };
    } else {
      initializePaymentForm();
    }
  }, []);
  
  const initializePaymentForm = async () => {
    if (!window.Square) return;
    
    try {
      // Get environment configuration
      const appId = process.env.NEXT_PUBLIC_SQUARE_APPLICATION_ID;
      const locationId = process.env.NEXT_PUBLIC_SQUARE_LOCATION_ID;

      if (!appId || !locationId) {
        throw new Error('Square configuration missing. Please check environment variables.');
      }

      // Initialize Square
      const payments = window.Square.payments(appId, locationId);
      
      // Create card payment method
      const card = await payments.card();
      await card.attach('#square-card-container');
      
      setPaymentForm(card);
      setIsLoading(false);
    } catch (error) {
      console.error('Error initializing Square payment form:', error);
      setIsLoading(false);
      setErrorMessage('Failed to initialize payment form. Please try again later.');
      onError(error);
    }
  };
  
  const handlePayment = async (event) => {
    event.preventDefault();
    
    if (!paymentForm) {
      setErrorMessage('Payment form not initialized');
      return;
    }
    
    try {
      setIsLoading(true);
      
      // Tokenize payment method
      const result = await paymentForm.tokenize();
      if (result.status === 'OK') {
        // Process payment with the token via public API
        try {
          const paymentResponse = await fetch('/api/public/process-payment', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              token: result.token,
              amount: amount,
              currency: currency,
              orderDetails: orderDetails
            }),
          });

          const paymentResult = await paymentResponse.json();

          if (!paymentResponse.ok) {
            throw new Error(paymentResult.error || 'Payment processing failed');
          }

          if (paymentResult.success) {
            onSuccess({
              paymentId: paymentResult.paymentId,
              paymentStatus: 'COMPLETED',
              paymentDetails: {
                token: result.token,
                amount: amount,
                currency: currency,
                transactionId: paymentResult.transactionId
              }
            });
          } else {
            throw new Error(paymentResult.error || 'Payment processing failed');
          }
        } catch (error) {
          console.error('Payment processing error:', error);
          setErrorMessage(error.message || 'Payment processing failed. Please try again.');
          onError(error);
        }
        setIsLoading(false);
      } else {
        setErrorMessage(result.errors[0].message);
        setIsLoading(false);
        onError(result.errors);
      }
    } catch (error) {
      console.error('Error processing Square payment:', error);
      setErrorMessage('Payment processing failed. Please try again.');
      setIsLoading(false);
      onError(error);
    }
  };
  
  return (
    <div className={styles.squarePaymentContainer}>
      {isLoading && (
        <div className={styles.loading}>
          <div className={styles.spinner}></div>
          <p>Processing payment...</p>
        </div>
      )}
      
      {errorMessage && (
        <div className={styles.error}>
          {errorMessage}
        </div>
      )}
      
      <form className={styles.paymentForm} onSubmit={handlePayment}>
        <div className={styles.formGroup}>
          <label htmlFor="square-card-container">Card Details</label>
          <div id="square-card-container" className={styles.cardContainer}></div>
        </div>
        
        <button 
          type="submit" 
          className={styles.payButton}
          disabled={isLoading || !paymentForm}
        >
          Pay ${amount.toFixed(2)} {currency}
        </button>
      </form>
      
      <div className={styles.securePaymentNote}>
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M8 0C5.243 0 3 2.243 3 5V6H2C1.447 6 1 6.447 1 7V15C1 15.553 1.447 16 2 16H14C14.553 16 15 15.553 15 15V7C15 6.447 14.553 6 14 6H13V5C13 2.243 10.757 0 8 0ZM5 5C5 3.346 6.346 2 8 2C9.654 2 11 3.346 11 5V6H5V5ZM8 12C7.172 12 6.5 11.328 6.5 10.5C6.5 9.672 7.172 9 8 9C8.828 9 9.5 9.672 9.5 10.5C9.5 11.328 8.828 12 8 12Z" fill="#4CAF50"/>
        </svg>
        <span>Secure payment processing by Square</span>
      </div>
    </div>
  );
};

export default SquarePaymentForm;
