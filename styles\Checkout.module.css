.main {
  padding: 2rem 0;
  min-height: calc(100vh - 80px);
  background-color: rgba(250, 250, 255, 0.8);
}

.checkoutSection {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

.pageTitle {
  font-size: 2.5rem;
  margin-bottom: 2rem;
  text-align: center;
  color: #333;
}

.checkoutContainer {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 2rem;
}

.checkoutForm {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  padding: 2rem;
}

.formSection {
  margin-bottom: 2rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid #eee;
}

.formSection:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.sectionTitle {
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  color: #333;
}

.formRow {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1rem;
}

.formGroup {
  margin-bottom: 1.5rem;
}

.formGroup label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #333;
}

.formGroup input,
.formGroup select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

.formGroup input:focus,
.formGroup select:focus {
  outline: none;
  border-color: #1A73E8;
}

.inputError {
  border-color: #d32f2f !important;
}

.errorMessage {
  color: #d32f2f;
  font-size: 0.875rem;
  margin-top: 0.25rem;
  display: block;
}

.required {
  color: #d32f2f;
}

.orderSummary {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  padding: 2rem;
  align-self: start;
  position: sticky;
  top: 2rem;
}

.summaryTitle {
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  color: #333;
}

.cartItems {
  margin-bottom: 1.5rem;
  max-height: 300px;
  overflow-y: auto;
}

.cartItem {
  display: flex;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #eee;
}

.cartItem:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.cartItemImage {
  width: 60px;
  height: 60px;
  border-radius: 4px;
  overflow: hidden;
  margin-right: 1rem;
}

.cartItemImage img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cartItemDetails {
  flex: 1;
}

.cartItemName {
  font-size: 1rem;
  margin: 0 0 0.5rem;
  color: #333;
}

.cartItemPrice {
  display: flex;
  justify-content: space-between;
  color: #555;
  font-size: 0.9rem;
}

.summaryDetails {
  margin-bottom: 1.5rem;
}

.summaryRow {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.75rem;
  font-size: 1rem;
  color: #555;
}

.summaryTotal {
  font-weight: 700;
  color: #333;
  font-size: 1.25rem;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #eee;
}

.policyLinks {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.policyLinks a {
  color: #1A73E8;
  font-size: 0.875rem;
  text-decoration: none;
}

.policyLinks a:hover {
  text-decoration: underline;
}

.customerFormContainer {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  padding: 2rem;
  margin-bottom: 2rem;
}

.customerSummary {
  margin-bottom: 1.5rem;
  padding: 1.5rem;
  background-color: rgba(248, 249, 250, 0.5);
  border-radius: 8px;
  border-left: 4px solid #4ECDC4;
  position: relative;
}

.customerSummary h3 {
  margin-top: 0;
  margin-bottom: 1rem;
  color: #333;
  font-size: 1.2rem;
}

.customerSummary p {
  margin: 0.5rem 0;
  color: #555;
}

.addressDetails {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px dashed #ddd;
}

.editButton {
  display: inline-block;
  margin-top: 1rem;
  padding: 8px 16px;
  background-color: transparent;
  border: 1px solid #4ECDC4;
  color: #4ECDC4;
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.editButton:hover {
  background-color: #4ECDC4;
  color: white;
}

.checkboxGroup {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  margin-top: 1rem;
}

.checkboxGroup input {
  width: auto;
  margin-top: 0.25rem;
}

.checkboxGroup label {
  font-size: 0.9rem;
  line-height: 1.4;
  font-weight: normal;
}

.paymentInfo {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.paymentLogos {
  display: flex;
  gap: 1.5rem;
  align-items: center;
}

.checkoutButton {
  background-color: #4ecdc4;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 100%;
  text-align: center;
}

.checkoutButton:hover {
  background-color: #40b0a8;
  transform: translateY(-2px);
}

.securePaymentNote {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #4CAF50;
}

.secureCheckout {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px dashed #eee;
}

.secureCheckoutHeader {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #4CAF50;
  margin-bottom: 1rem;
}

/* Payment Form Styles */
.paymentFormContainer {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  padding: 2rem;
  margin-bottom: 2rem;
}

.squarePaymentSection {
  margin-top: 2rem;
  padding: 1.5rem;
  background-color: rgba(248, 249, 250, 0.5);
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.paymentError {
  background-color: #ffebee;
  border: 1px solid #f44336;
  border-radius: 4px;
  padding: 1rem;
  margin-bottom: 1.5rem;
  color: #d32f2f;
  font-size: 0.9rem;
}

.processingOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.processingContent {
  background-color: white;
  padding: 2rem;
  border-radius: 8px;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #4ECDC4;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.orderCompleteContainer {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  padding: 2rem;
  text-align: center;
  margin-bottom: 2rem;
}

/* Responsive styles */
@media (max-width: 992px) {
  .checkoutContainer {
    grid-template-columns: 1fr;
  }
  
  .orderSummary {
    position: static;
    margin-top: 2rem;
  }
}

@media (max-width: 640px) {
  .formRow {
    grid-template-columns: 1fr;
  }
  
  .pageTitle {
    font-size: 2rem;
  }
}
