import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import Link from 'next/link'
import AdminLayout from '@/components/admin/AdminLayout'
import Modal from '@/components/admin/Modal'
import styles from '@/styles/admin/marketing/SegmentDetail.module.css'

export default function SegmentDetail() {
  const router = useRouter()
  const { id } = router.query
  const [segment, setSegment] = useState(null)
  const [customers, setCustomers] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [deleteLoading, setDeleteLoading] = useState(false)
  const [deleteError, setDeleteError] = useState(null)
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0
  })

  // Fetch segment data
  useEffect(() => {
    if (!id) return

    const fetchSegment = async () => {
      setLoading(true)
      setError(null)

      try {
        const response = await fetch(`/api/marketing/segments/${id}`)
        
        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || 'Failed to fetch segment')
        }

        const data = await response.json()
        setSegment(data.segment || null)
        
        // Preview segment customers
        const previewResponse = await fetch(`/api/marketing/segments/${id}?action=preview`, {
          method: 'POST'
        })
        
        if (!previewResponse.ok) {
          const errorData = await previewResponse.json()
          throw new Error(errorData.error || 'Failed to preview segment customers')
        }
        
        const previewData = await previewResponse.json()
        setCustomers(previewData.customers || [])
        setPagination({ ...pagination, total: previewData.total || 0 })
      } catch (error) {
        console.error('Error fetching segment:', error)
        setError(error.message)
      } finally {
        setLoading(false)
      }
    }

    fetchSegment()
  }, [id])

  // Handle delete segment
  const handleDeleteSegment = async () => {
    setDeleteLoading(true)
    setDeleteError(null)

    try {
      const response = await fetch(`/api/marketing/segments/${id}`, {
        method: 'DELETE'
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete segment')
      }

      // Redirect to segment list
      router.push('/admin/marketing/segments')
    } catch (error) {
      console.error('Error deleting segment:', error)
      setDeleteError(error.message)
    } finally {
      setDeleteLoading(false)
    }
  }

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return '-'
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (loading) {
    return (
      <AdminLayout>
        <div className={styles.segmentDetail}>
          <div className={styles.loading}>Loading segment data...</div>
        </div>
      </AdminLayout>
    )
  }

  if (error) {
    return (
      <AdminLayout>
        <div className={styles.segmentDetail}>
          <div className={styles.error}>
            Error: {error}
            <button
              className={styles.backButton}
              onClick={() => router.push('/admin/marketing/segments')}
            >
              Back to Segments
            </button>
          </div>
        </div>
      </AdminLayout>
    )
  }

  if (!segment) {
    return (
      <AdminLayout>
        <div className={styles.segmentDetail}>
          <div className={styles.notFound}>
            Segment not found
            <button
              className={styles.backButton}
              onClick={() => router.push('/admin/marketing/segments')}
            >
              Back to Segments
            </button>
          </div>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className={styles.segmentDetail}>
        <div className={styles.header}>
          <div className={styles.headerLeft}>
            <h2>{segment.name}</h2>
            <div className={styles.meta}>
              <span className={styles.metaItem}>
                Created: {formatDate(segment.created_at)}
              </span>
              {segment.updated_at && segment.updated_at !== segment.created_at && (
                <span className={styles.metaItem}>
                  Updated: {formatDate(segment.updated_at)}
                </span>
              )}
            </div>
          </div>
          <div className={styles.headerActions}>
            <Link href={`/admin/marketing/segments/${id}/edit`}>
              <a className={styles.editButton}>Edit Segment</a>
            </Link>
            <Link href={`/admin/marketing/campaigns/new?segment=${id}`}>
              <a className={styles.campaignButton}>Create Campaign</a>
            </Link>
            <button
              className={styles.deleteButton}
              onClick={() => setShowDeleteModal(true)}
            >
              Delete
            </button>
          </div>
        </div>

        <div className={styles.segmentInfo}>
          <div className={styles.infoSection}>
            <h3>Segment Details</h3>
            <div className={styles.infoGrid}>
              <div className={styles.infoItem}>
                <span className={styles.infoLabel}>Description</span>
                <span className={styles.infoValue}>
                  {segment.description || 'No description provided'}
                </span>
              </div>
              <div className={styles.infoItem}>
                <span className={styles.infoLabel}>Customer Count</span>
                <span className={styles.infoValue}>
                  {segment.customer_count || 0}
                </span>
              </div>
            </div>
          </div>

          <div className={styles.infoSection}>
            <h3>Matching Customers</h3>
            {customers.length === 0 ? (
              <div className={styles.noResults}>
                No customers match this segment's criteria
              </div>
            ) : (
              <div className={styles.customerTable}>
                <table>
                  <thead>
                    <tr>
                      <th>Name</th>
                      <th>Email</th>
                      <th>Phone</th>
                      <th>Location</th>
                      <th>Marketing Consent</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {customers.map((customer) => (
                      <tr key={customer.id}>
                        <td>{customer.name}</td>
                        <td>{customer.email}</td>
                        <td>{customer.phone || '-'}</td>
                        <td>
                          {customer.city ? `${customer.city}, ${customer.state || ''}` : '-'}
                        </td>
                        <td>
                          {customer.marketing_consent ? (
                            <span className={styles.consentGranted}>Yes</span>
                          ) : (
                            <span className={styles.consentDenied}>No</span>
                          )}
                        </td>
                        <td>
                          <Link
                            href={`/admin/customers/${customer.id}`}
                            className={styles.viewCustomerButton}
                          >
                            View
                          </Link>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
                {pagination.total > customers.length && (
                  <div className={styles.moreCustomers}>
                    + {pagination.total - customers.length} more customers
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Delete Confirmation Modal */}
        {showDeleteModal && (
          <Modal onClose={() => setShowDeleteModal(false)}>
            <div className={styles.deleteModal}>
              <h3>Delete Segment</h3>
              <p>
                Are you sure you want to delete the segment "{segment.name}"?
                This action cannot be undone.
              </p>
              {deleteError && (
                <div className={styles.deleteError}>
                  Error: {deleteError}
                </div>
              )}
              <div className={styles.deleteActions}>
                <button
                  className={styles.cancelDeleteButton}
                  onClick={() => setShowDeleteModal(false)}
                  disabled={deleteLoading}
                >
                  Cancel
                </button>
                <button
                  className={styles.confirmDeleteButton}
                  onClick={handleDeleteSegment}
                  disabled={deleteLoading}
                >
                  {deleteLoading ? 'Deleting...' : 'Delete Segment'}
                </button>
              </div>
            </div>
          </Modal>
        )}
      </div>
    </AdminLayout>
  )
}
