// Customer API endpoint for public-facing registration and management
import { getAdminClient } from '@/lib/supabase';

/**
 * Public API endpoint for handling customer registration and management
 * This endpoint handles both guest checkouts and registered users
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 * @returns {Object} - JSON response
 */
export default async function handler(req, res) {
  switch (req.method) {
    case 'POST':
      return handleCustomerCreation(req, res);
    case 'PUT':
      return handleCustomerUpdate(req, res);
    case 'GET':
      return handleCustomerFetch(req, res);
    default:
      return res.status(405).json({ error: 'Method not allowed' });
  }
}

/**
 * Handle customer creation/registration
 * Supports both guest and full registration paths
 */
async function handleCustomerCreation(req, res) {
  try {
    // Get admin client for this operation
    const adminClient = getAdminClient();
    if (!adminClient) {
      console.error("Supabase admin client not available.");
      return res.status(500).json({ error: 'Service unavailable' });
    }

    const {
      name,
      email,
      phone,
      password = null, // Optional for guest checkout
      address = null,
      city = null,
      state = null,
      postal_code = null,
      country = 'Australia',
      marketingConsent = false,
      isGuest = true // Default to guest checkout
    } = req.body;

    // Validate required fields
    if (!name || !email || !phone) {
      return res.status(400).json({ error: 'Missing required fields: name, email, and phone are required' });
    }    // Check if customer already exists - get full customer data
    const { data: existingCustomer, error: customerCheckError } = await adminClient
      .from('customers')
      .select('*')
      .eq('email', email)
      .single();

    if (customerCheckError && customerCheckError.code !== 'PGRST116') {
      console.error('Error checking for existing customer:', customerCheckError);
      throw customerCheckError;
    }

    // If customer exists, return their complete data
    if (existingCustomer) {
      return res.status(200).json({
        message: 'Customer already exists',
        customer: {
          id: existingCustomer.id,
          name: existingCustomer.name,
          email: existingCustomer.email,
          phone: existingCustomer.phone,
          address: existingCustomer.address,
          city: existingCustomer.city,
          state: existingCustomer.state,
          postal_code: existingCustomer.postal_code,
          country: existingCustomer.country,
          marketing_consent: existingCustomer.marketing_consent,
          isExisting: true
        }
      });
    }    // Create customer in the database
    const { data: newCustomer, error: createCustomerError } = await adminClient
      .from('customers')
      .insert([{
        name,
        email,
        phone,
        address,
        city,
        state,
        postal_code,
        country,
        marketing_consent: marketingConsent
      }])
      .select();

    if (createCustomerError) {
      console.error('Error creating customer:', createCustomerError);
      throw createCustomerError;
    }

    let authUser = null;    // If not a guest, create auth user account
    if (!isGuest && password) {
      const { data: user, error: authError } = await adminClient.auth.admin.createUser({
        email,
        password,
        email_confirm: true,
        user_metadata: {
          name,
          customer_id: newCustomer[0].id
        }
      });

      if (authError) {
        console.error('Error creating auth user:', authError);
        // Continue anyway - customer record is created, but auth failed
      } else {
        authUser = user;
      }
    }

    return res.status(201).json({
      message: 'Customer created successfully',
      customer: {
        id: newCustomer[0].id,
        name: newCustomer[0].name,
        email: newCustomer[0].email,
        phone: newCustomer[0].phone,
        address: newCustomer[0].address,
        city: newCustomer[0].city,
        state: newCustomer[0].state,
        postal_code: newCustomer[0].postal_code,
        country: newCustomer[0].country,
        marketing_consent: newCustomer[0].marketing_consent,
        isGuest,
        authUser: authUser ? {
          id: authUser.id,
          email: authUser.email
        } : null
      }
    });
  } catch (error) {
    console.error('Customer creation error:', error);
    return res.status(500).json({
      error: 'Failed to create customer',
      details: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
}

/**
 * Handle customer data updates
 */
async function handleCustomerUpdate(req, res) {
  try {
    const { id, ...updateData } = req.body;

    // Validate required fields
    if (!id) {
      return res.status(400).json({ error: 'Missing customer ID' });
    }

    // Handle marketing consent separately to ensure it's properly typed
    if (updateData.marketingConsent !== undefined) {
      updateData.marketing_consent = !!updateData.marketingConsent;
      delete updateData.marketingConsent;
    }

    // Update customer in database
    const adminClient = getAdminClient();
    if (!adminClient) {
      console.error("Supabase admin client not available.");
      return res.status(500).json({ error: 'Service unavailable' });
    }

    const { data: updatedCustomer, error: updateError } = await adminClient
      .from('customers')
      .update(updateData)
      .eq('id', id)
      .select();

    if (updateError) {
      console.error('Error updating customer:', updateError);
      throw updateError;
    }

    return res.status(200).json({
      message: 'Customer updated successfully',
      customer: updatedCustomer[0]
    });
  } catch (error) {
    console.error('Customer update error:', error);
    return res.status(500).json({
      error: 'Failed to update customer',
      details: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
}

/**
 * Fetch customer data by email or ID
 */
async function handleCustomerFetch(req, res) {
  try {
    const { email, id } = req.query;

    if (!email && !id) {
      return res.status(400).json({ error: 'Missing email or ID parameter' });
    }

    const adminClient = getAdminClient();
    if (!adminClient) {
      console.error("Supabase admin client not available.");
      return res.status(500).json({ error: 'Service unavailable' });
    }

    let query = adminClient.from('customers').select('*');

    if (id) {
      query = query.eq('id', id);
    } else if (email) {
      query = query.eq('email', email);
    }

    const { data: customer, error: fetchError } = await query.single();

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return res.status(404).json({ error: 'Customer not found' });
      }
      console.error('Error fetching customer:', fetchError);
      throw fetchError;
    }

    return res.status(200).json({
      customer
    });
  } catch (error) {
    console.error('Customer fetch error:', error);
    return res.status(500).json({
      error: 'Failed to fetch customer',
      details: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
}
