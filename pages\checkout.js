import { useState, useEffect } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { useRouter } from 'next/router';
import styles from '@/styles/Checkout.module.css';
import Layout from '@/components/Layout';
import AnimatedSection from '@/components/AnimatedSection';
import CustomerForm from '@/components/CustomerForm';
import SquarePaymentForm from '@/components/SquarePaymentForm';
import { useCustomer } from '@/contexts/CustomerContext';
import { useCart } from '@/contexts/CartContext'; // Import useCart
import SquarePaymentForm from '@/components/SquarePaymentForm'; // Import SquarePaymentForm

export default function Checkout() {
  const router = useRouter();
  const { cart: cartFromContext, clearCart, getCartTotal, getCartItemCount } = useCart(); // Use CartContext
  const { customer, saveGuestCustomer, loading: customerLoading } = useCustomer();
  const [customerStep, setCustomerStep] = useState(true); // true = customer info, false = payment
  const [paymentStep, setPaymentStep] = useState(false); // true = show payment form
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);
  const [paymentError, setPaymentError] = useState(null);
  const [customerFormData, setCustomerFormData] = useState(null); // Store customer form data for editing
  const [orderSummary, setOrderSummary] = useState({
    subtotal: 0,
    shipping: 10.00, // Default shipping
    total: 0,
  });
  const [paymentError, setPaymentError] = useState('');
  // Removed Square settings state variables:
  // const [squareAppId, setSquareAppId] = useState('');
  // const [squareLocationId, setSquareLocationId] = useState('');
  // const [squareSettingsLoading, setSquareSettingsLoading] = useState(true);
  // const [squareSettingsError, setSquareSettingsError] = useState(null);

  // Removed useEffect for fetching Square settings.

  // Calculate order summary based on cartFromContext
  useEffect(() => {
    if (getCartItemCount() === 0 && !customerStep) { // If cart becomes empty and not on customer step, redirect
      router.push('/shop');
      return;
    }

    const subtotal = getCartTotal();
    const shipping = subtotal > 100 ? 0 : 10.00; // Free shipping for orders over $100
    setOrderSummary({
      subtotal,
      shipping,
      total: subtotal + shipping,
    });
  }, [cartFromContext, getCartTotal, getCartItemCount, router, customerStep]);


  // Handle customer form completion
  const handleCustomerFormComplete = (customerData) => {
    console.log('Customer form completed with data:', customerData);
<<<<<<< HEAD
    // Store the customer form data for potential editing
    setCustomerFormData(customerData);
=======
    // If cart is empty at this point, perhaps redirect or show message
    if (getCartItemCount() === 0) {
        alert('Your cart is empty. Please add items to your cart before proceeding.');
        router.push('/shop');
        return;
    }
>>>>>>> f4997080479c973bb3d714940b27bf34a6d3ee32
    setCustomerStep(false); // Proceed to payment step
    setPaymentStep(true); // Show payment form
  };

<<<<<<< HEAD
  // Handle editing customer information
  const handleEditCustomerInfo = () => {
    setPaymentStep(false);
    setCustomerStep(true);
    // Customer form will be pre-populated with existing data via CustomerForm component
  };
=======
  // Handle successful payment from SquarePaymentForm
  const handlePaymentSuccess = async (paymentResponse) => {
    setPaymentError(''); // Clear any previous payment errors
    console.log('Payment successful:', paymentResponse);

    if (!customer) {
      alert('Customer information is missing. Please complete the customer form.');
      setCustomerStep(true); // Go back to customer step
      return;
    }
>>>>>>> f4997080479c973bb3d714940b27bf34a6d3ee32

  // Handle Square payment success
  const handlePaymentSuccess = async (paymentResult) => {
    try {
<<<<<<< HEAD
      setIsProcessingPayment(true);
      setPaymentError(null);

      // Create order with payment details
=======
>>>>>>> f4997080479c973bb3d714940b27bf34a6d3ee32
      const orderData = {
        customer_id: customer.id, // Assuming customer object has an id
        customer_details: { // Send customer snapshot
            name: customer.name,
            email: customer.email,
            phone: customer.phone,
            address: customer.address,
            city: customer.city,
            state: customer.state,
            postal_code: customer.postal_code,
            country: customer.country,
        },
        items: cartFromContext,
        subtotal: orderSummary.subtotal,
        shipping: orderSummary.shipping,
        total: orderSummary.total,
<<<<<<< HEAD
        status: 'COMPLETED',
        payment_id: paymentResult.paymentId,
        payment_status: paymentResult.paymentStatus,
        payment_details: paymentResult.paymentDetails
=======
        payment_id: paymentResponse.paymentId, // From Square via our backend
        payment_status: paymentResponse.status, // From Square via our backend
        status: 'COMPLETED', // Or map from paymentResponse.status
>>>>>>> f4997080479c973bb3d714940b27bf34a6d3ee32
      };

      const response = await fetch('/api/public/orders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(orderData),
      });

      const result = await response.json();

      if (!response.ok) {
<<<<<<< HEAD
        throw new Error(result.error || 'Error creating order');
=======
        throw new Error(result.error || 'Error submitting order after payment');
>>>>>>> f4997080479c973bb3d714940b27bf34a6d3ee32
      }

      clearCart(); // Clear cart from context

      router.push({
        pathname: '/confirmation',
        query: {
          orderId: result.order.id,
<<<<<<< HEAD
          paymentId: paymentResult.paymentId,
          status: 'COMPLETED'
        }
      });
    } catch (error) {
      console.error('Order creation error:', error);
      setPaymentError('Payment successful but order creation failed. Please contact support.');
      setIsProcessingPayment(false);
    }
  };

  // Handle Square payment error
  const handlePaymentError = (error) => {
    console.error('Payment error:', error);
    setPaymentError(error.message || 'Payment failed. Please try again.');
    setIsProcessingPayment(false);
  };


=======
          paymentId: paymentResponse.paymentId,
          status: orderData.status,
          total: orderSummary.total,
        },
      });
    } catch (error) {
      console.error('Order submission error after payment:', error);
      setPaymentError(`There was an error finalizing your order: ${error.message}. Please contact support.`);
      // Potentially need to handle refund or void payment if order submission fails critically
    }
  };

  // Handle payment error from SquarePaymentForm
  const handlePaymentError = (errorMsg) => {
    console.error('Payment error:', errorMsg);
    setPaymentError(`Payment failed: ${errorMsg}. Please check your card details or try another card.`);
  };

  // The main form onSubmit is no longer used for direct submission
  // It's kept if SquareForm is embedded within it, but SquareForm has its own submit.
>>>>>>> f4997080479c973bb3d714940b27bf34a6d3ee32

  return (
    <Layout>
      <Head>
        <title>Checkout | OceanSoulSparkles</title>
        <meta name="description" content="Complete your purchase from OceanSoulSparkles. Secure checkout with multiple payment options." />
      </Head>

      <main className={styles.main}>
        <section className={styles.checkoutSection}>
          <AnimatedSection animation="fade-up">
            <h1 className={styles.pageTitle}>Checkout</h1>

            <div className={styles.checkoutContainer}>
              <div className={styles.checkoutForm}>
                {customerStep ? (
                  <div className={styles.customerFormContainer}>
                    <h2 className={styles.sectionTitle}>Customer Information</h2>
                    <CustomerForm
                      isCheckout={true}
                      editingData={customerFormData}
                      onComplete={handleCustomerFormComplete}
                      // Pass initial customer data if available for editing
                      initialData={customer || {}}
                    />
                  </div>
<<<<<<< HEAD
                ) : paymentStep ? (
                  <div className={styles.paymentFormContainer}>
                    <h2 className={styles.sectionTitle}>Review & Payment</h2>



                    {/* Use customerFormData as primary source, fallback to customer context */}
                    {(customerFormData || customer) && (
                      <div className={styles.customerReviewSection}>
                        <div className={styles.reviewHeader}>
                          <h3>Customer Information</h3>
                          <button
                            type="button"
=======
                ) : (
                  // Removed onSubmit={handleSubmit} from form tag as Square handles submission trigger
                  <div className={styles.paymentSection}>
                    <div className={styles.formSection}>
                      <h2 className={styles.sectionTitle}>Shipping Information</h2>
                      {customer && (
                        <div className={styles.customerSummary}>
                          <h3>Customer Details</h3>
                          <p><strong>Name:</strong> {customer.name}</p>
                          <p><strong>Email:</strong> {customer.email}</p>
                          <p><strong>Phone:</strong> {customer.phone}</p>
                          {customer.address && (
                            <div className={styles.addressDetails}>
                              <p><strong>Address:</strong> {customer.address}</p>
                              <p><strong>City:</strong> {customer.city}, {customer.state} {customer.postal_code}</p>
                              <p><strong>Country:</strong> {customer.country}</p>
                            </div>
                          )}
                          <button 
                            type="button" 
>>>>>>> f4997080479c973bb3d714940b27bf34a6d3ee32
                            className={styles.editButton}
                            onClick={handleEditCustomerInfo}
                          >
                            Edit Information
                          </button>
                        </div>

                        <div className={styles.customerDetails}>
                          <div className={styles.detailsGrid}>
                            <div className={styles.contactInfo}>
                              <h4>Contact Details</h4>
                              <div className={styles.infoItem}>
                                <span className={styles.label}>Name:</span>
                                <span className={styles.value}>{(customerFormData || customer)?.name}</span>
                              </div>
                              <div className={styles.infoItem}>
                                <span className={styles.label}>Email:</span>
                                <span className={styles.value}>{(customerFormData || customer)?.email}</span>
                              </div>
                              <div className={styles.infoItem}>
                                <span className={styles.label}>Phone:</span>
                                <span className={styles.value}>{(customerFormData || customer)?.phone}</span>
                              </div>
                            </div>

<<<<<<< HEAD
                            {(customerFormData || customer)?.address && (
                              <div className={styles.shippingInfo}>
                                <h4>Shipping Address</h4>
                                <div className={styles.addressBlock}>
                                  <div>{(customerFormData || customer)?.address}</div>
                                  <div>
                                    {(customerFormData || customer)?.city}, {(customerFormData || customer)?.state} {(customerFormData || customer)?.postal_code}
                                  </div>
                                  <div>{(customerFormData || customer)?.country}</div>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
=======
                      <div className={styles.paymentInfo}>
                        <p>We accept payments via Square:</p>
                        <div className={styles.paymentLogos}>
                          <img src="/images/logos/square.png" alt="Square Payments" width={120} height={30} />
                        </div>

                        {/* Display payment error message */}
                        {paymentError && (
                          <div className={`${styles.paymentError} ${styles.generalMessage}`}>
                            <p>{paymentError}</p>
                          </div>
                        )}

                        {/* Conditional rendering for Square settings loading/error removed. */}
                        {/* SquarePaymentForm is now rendered directly, assuming env vars are set. */}
                        <SquarePaymentForm
                          amount={orderSummary.total}
                          currency="AUD"
                          onSuccess={handlePaymentSuccess}
                          onError={handlePaymentError}
                          orderDetails={{
                             items: cartFromContext.map(item => ({ id: item.id, name: item.name, quantity: item.quantity, price: item.price })),
                             customerId: customer?.id,
                             customerName: customer?.name,
                             customerEmail: customer?.email,
                             customerPhone: customer?.phone,
                             shippingAddress: {
                               name: customer?.name,
                               address1: customer?.address,
                               address2: customer?.address_line_2 || '',
                               city: customer?.city,
                               state: customer?.state,
                               postalCode: customer?.postal_code,
                               country: customer?.country,
                             },
                             subtotal: orderSummary.subtotal,
                             shippingCost: orderSummary.shipping,
                           }}
                          // applicationId and locationId props removed
                        />
>>>>>>> f4997080479c973bb3d714940b27bf34a6d3ee32
                      </div>
                    )}

                    {paymentError && (
                      <div className={styles.paymentError}>
                        <p>{paymentError}</p>
                      </div>
                    )}

                    <div className={styles.squarePaymentSection}>
                      <SquarePaymentForm
                        amount={orderSummary.total}
                        currency="AUD"
                        onSuccess={handlePaymentSuccess}
                        onError={handlePaymentError}
                        orderDetails={{
                          productName: cart.length > 1 ? 'Multiple Products' : cart[0]?.name,
                          customerName: customer?.name,
                          orderTotal: orderSummary.total
                        }}
                      />
                    </div>
<<<<<<< HEAD

                    {isProcessingPayment && (
                      <div className={styles.processingOverlay}>
                        <div className={styles.processingContent}>
                          <div className={styles.loadingSpinner}></div>
                          <p>Processing your order...</p>
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className={styles.orderCompleteContainer}>
                    <h2 className={styles.sectionTitle}>Order Complete</h2>
                    <p>Your payment has been processed successfully!</p>
=======
>>>>>>> f4997080479c973bb3d714940b27bf34a6d3ee32
                  </div>
                )}
              </div>

              <div className={styles.orderSummary}>
                <h2 className={styles.summaryTitle}>Order Summary</h2>

                <div className={styles.cartItems}>
                  {cartFromContext.map(item => (
                    <div key={item.id} className={styles.cartItem}>
                      <div className={styles.cartItemImage}>
                        <img src={item.image || '/images/placeholder.svg'} alt={item.name} />
                      </div>
                      <div className={styles.cartItemDetails}>
                        <h3 className={styles.cartItemName}>{item.name}</h3>
                        <div className={styles.cartItemPrice}>
                          <span>${parseFloat(item.price || 0).toFixed(2)}</span>
                          <span>x {item.quantity}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                <div className={styles.summaryDetails}>
                  <div className={styles.summaryRow}>
                    <span>Subtotal</span>
                    <span>${orderSummary.subtotal.toFixed(2)}</span>
                  </div>
                  <div className={styles.summaryRow}>
                    <span>Shipping</span>
                    <span>${orderSummary.shipping.toFixed(2)}</span>
                  </div>
                  <div className={`${styles.summaryRow} ${styles.summaryTotal}`}>
                    <span>Total</span>
                    <span>${orderSummary.total.toFixed(2)}</span>
                  </div>
                </div>

                <div className={styles.policyLinks}>
                  <Link href="/policies#shipping-info">Shipping Information</Link>
                  <Link href="/policies#return-policy">Return & Refund Policy</Link>
                </div>

                <div className={styles.secureCheckout}>
                  <div className={styles.secureCheckoutHeader}>
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M8 0C5.243 0 3 2.243 3 5V6H2C1.447 6 1 6.447 1 7V15C1 15.553 1.447 16 2 16H14C14.553 16 15 15.553 15 15V7C15 6.447 14.553 6 14 6H13V5C13 2.243 10.757 0 8 0ZM5 5C5 3.346 6.346 2 8 2C9.654 2 11 3.346 11 5V6H5V5ZM8 12C7.172 12 6.5 11.328 6.5 10.5C6.5 9.672 7.172 9 8 9C8.828 9 9.5 9.672 9.5 10.5C9.5 11.328 8.828 12 8 12Z" fill="#4CAF50"/>
                    </svg>
                    <span>Secure Checkout</span>
                  </div>

                  <div className={styles.paymentLogos}>
                    <img src="/images/logos/square.png" alt="Square Payments" width={80} height={20} />
                  </div>
                </div>
              </div>
            </div>
          </AnimatedSection>
        </section>
      </main>
    </Layout>
  );
}
